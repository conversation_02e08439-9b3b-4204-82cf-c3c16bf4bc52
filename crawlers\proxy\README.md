# IP代理缓存机制

## 概述

这个模块实现了一个智能的IP代理缓存机制，类似于Cookie缓存的实现方式。它能够自动管理代理的获取、缓存、过期和刷新，确保爬虫始终使用有效的代理进行数据抓取。

## 特性

- **自动缓存管理**: 代理自动缓存1分钟，过期后自动获取新代理
- **失败重试机制**: 支持失败计数，失败3次后自动获取新代理
- **多种缓存类型**: 支持内存缓存和Redis缓存
- **状态监控**: 提供详细的代理状态信息
- **强制刷新**: 支持手动强制刷新代理
- **无平台概念**: 代理缓存不区分平台，全局共享

## 文件结构

```
crawlers/proxy/
├── proxy_cache.py          # 代理缓存核心实现
├── proxy_cache_example.py  # 使用示例
├── test_proxy_cache.py     # 测试文件
├── qing_guo.py            # 青果代理客户端
└── README.md              # 说明文档
```

## 核心类

### ProxyCache

主要的代理缓存管理类，提供以下功能：

#### 初始化参数

```python
ProxyCache(
    auth_key: str,              # 代理认证密钥
    auth_password: str,         # 代理认证密码
    proxy_refresh_interval: int = 1,  # 代理刷新间隔(分钟)
    cache_type: str = "redis"   # 缓存类型
)
```

#### 主要方法

- `get_proxy()`: 获取可用代理，自动处理缓存和刷新
- `add_proxy(proxy_info)`: 手动添加代理到缓存
- `mark_failed()`: 标记代理请求失败
- `clear_failed_count()`: 清除失败计数
- `force_refresh()`: 强制刷新代理
- `get_proxy_status()`: 获取代理状态信息

## 使用方法

### 1. 基本使用

```python
from crawlers.proxy.proxy_cache import ProxyCache

# 初始化代理缓存
proxy_cache = ProxyCache(
    auth_key="your_auth_key",
    auth_password="your_auth_password",
    proxy_refresh_interval=1,  # 1分钟过期
    cache_type="redis"
)

# 获取代理
proxy_info = proxy_cache.get_proxy()
if proxy_info:
    proxy_url = proxy_info["proxy_url"]
    deadline = proxy_info["deadline"]
    print(f"代理: {proxy_url}, 过期时间: {deadline}")
```

### 2. 在RequestManager中使用

```python
from crawlers.utils.request_manager import RequestManager

# 初始化RequestManager（已集成代理缓存）
request_manager = RequestManager(
    browser_api_key="your_browser_key",
    proxy_auth_key="your_proxy_key",
    proxy_auth_password="your_proxy_password",
    proxy_refresh_interval=1,
    cache_type="redis"
)

# 获取代理URL
proxy_url = request_manager.get_proxy()

# 获取完整代理信息
proxy_info = request_manager.get_proxy_info()

# 标记请求成功/失败
request_manager.mark_request_success("douyin")
request_manager.mark_request_failed("douyin")
```

### 3. 在爬虫中的典型使用流程

```python
def crawl_with_proxy():
    # 1. 初始化请求管理器
    request_manager = RequestManager(...)
    
    # 2. 获取代理
    proxy_url = request_manager.get_proxy()
    if not proxy_url:
        print("无法获取代理")
        return
    
    # 3. 使用代理进行请求
    proxies = {
        "http": proxy_url,
        "https": proxy_url
    }
    
    try:
        response = requests.get(url, proxies=proxies)
        if response.status_code == 200:
            # 请求成功，清除失败计数
            request_manager.mark_request_success("platform")
            return response.json()
        else:
            # 请求失败，标记失败
            request_manager.mark_request_failed("platform")
    except Exception as e:
        # 请求异常，标记失败
        request_manager.mark_request_failed("platform")
        print(f"请求失败: {e}")
```

## 返回值格式

代理信息返回格式：

```python
{
    'proxy_url': '**********************************************',
    'deadline': '2025-06-28 16:05:44'
}
```

## 缓存策略

1. **过期时间**: 代理缓存1分钟后自动过期
2. **失败重试**: 失败3次后自动获取新代理
3. **deadline检查**: 检查代理服务商提供的过期时间
4. **自动刷新**: 过期后自动从青果代理获取新代理

## 配置说明

### 缓存类型

- `"memory"`: 内存缓存，适合单机测试
- `"redis"`: Redis缓存，适合生产环境和分布式部署

### 刷新间隔

- 默认1分钟过期，可根据需要调整
- 建议不要设置太短，避免频繁请求代理服务

## 注意事项

1. **认证信息**: 确保提供正确的代理认证密钥和密码
2. **网络连接**: 确保能够访问青果代理服务
3. **Redis配置**: 使用Redis缓存时确保Redis服务正常运行
4. **错误处理**: 代理获取失败时会返回None，需要做好错误处理
5. **并发安全**: 代理缓存支持并发访问，但建议在高并发场景下进行充分测试

## 测试

运行测试文件：

```bash
python crawlers/proxy/test_proxy_cache.py
```

查看使用示例：

```bash
python crawlers/proxy/proxy_cache_example.py
```

## 集成到现有项目

1. 更新RequestManager的初始化参数，添加`proxy_refresh_interval`
2. 使用新的代理获取方法
3. 适当调用成功/失败标记方法
4. 根据需要使用强制刷新功能
