# -*- coding: utf-8 -*-
# @Time : 2025/5/26 18:05
# <AUTHOR> cyf
# @File : cookie_cache.py

import json
from datetime import timedelta, datetime
from typing import Optional

from crawlers.cache.cache_factory import CacheFactory


class CookieCache:
    def __init__(self, cookie_refresh_interval: int = 30, cache_type: str = "redis"):
        """
        初始化Cookie缓存管理器

        Args:
            cookie_refresh_interval: Cookie刷新间隔(分钟),默认30分钟
            cache_type: 缓存类型，支持 'memory' 或 'redis'
        """
        self.cache = CacheFactory.create_cache(cache_type, cron_interval=10)
        self.refresh_interval = timedelta(minutes=cookie_refresh_interval)

    def add_cookie(self, platform: str, cookie: str) -> None:
        """
        添加或更新cookie

        Args:
            platform: 平台名称
            cookie: cookie字符串
        """
        cookie_info = {
            "value": cookie,
            "failed_count": 0,
            "last_refresh": datetime.now().isoformat(),
        }
        # 缓存30分钟
        self.cache.set(f"cookie:{platform}", json.dumps(cookie_info), 30 * 60)

    def get_cookie(self, platform: str) -> Optional[str]:
        """
        获取指定平台的cookie

        Args:
            platform: 平台名称

        Returns:
            如果cookie有效返回cookie字符串,否则返回None
        """
        cached_data = self.cache.get(f"cookie:{platform}")
        if not cached_data:
            return None

        try:
            cookie_info = json.loads(cached_data)
        except (json.JSONDecodeError, TypeError):
            return None

        # 检查失败次数
        if cookie_info.get("failed_count", 0) >= 3:
            self.cache.set(f"cookie:{platform}", "", 0)  # 立即过期
            return None

        # 检查是否需要刷新
        last_refresh = datetime.fromisoformat(cookie_info["last_refresh"])
        if datetime.now() - last_refresh > self.refresh_interval:
            self.cache.set(f"cookie:{platform}", "", 0)  # 立即过期
            return None

        return cookie_info["value"]

    def mark_failed(self, platform: str) -> None:
        """
        标记指定平台的cookie请求失败

        Args:
            platform: 平台名称
        """
        cached_data = self.cache.get(f"cookie:{platform}")
        if cached_data:
            try:
                cookie_info = json.loads(cached_data)
                cookie_info["failed_count"] = cookie_info.get("failed_count", 0) + 1
                self.cache.set(f"cookie:{platform}", json.dumps(cookie_info), 30 * 60)
            except (json.JSONDecodeError, TypeError):
                pass

    def clear_failed_count(self, platform: str) -> None:
        """
        清除指定平台的cookie失败次数

        Args:
            platform: 平台名称
        """
        cached_data = self.cache.get(f"cookie:{platform}")
        if cached_data:
            try:
                cookie_info = json.loads(cached_data)
                cookie_info["failed_count"] = 0
                self.cache.set(f"cookie:{platform}", json.dumps(cookie_info), 30 * 60)
            except (json.JSONDecodeError, TypeError):
                pass
