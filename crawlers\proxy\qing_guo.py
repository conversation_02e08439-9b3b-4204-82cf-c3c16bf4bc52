# -*- encoding:utf-8 -*-
from typing import Dict

import requests


class QIngGuo:
    base_url = "https://share.proxy.qg.net"

    def __init__(self, auth_key: str, auth_password: str):
        self.auth_key = auth_key
        self.auth_password = auth_password

    def pong_proxy(self, proxy_addr):
        targetURL = "https://test.ipw.cn"

        proxyUrl = f"http://{self.auth_key}:{self.auth_password}@{proxy_addr}"

        proxies = {
            "http": proxyUrl,
            "https": proxyUrl,
        }
        resp = requests.get(targetURL, proxies=proxies)
        print(resp.text)

    def apply_new_ip(self, num: int = 1) -> Dict:
        uri = "/get"

        param = {
            "key": self.auth_key,
            "num": 1
        }
        resp = requests.get(url=self.base_url + uri, params=param)
        print(resp.text)

        return resp.json()

    def query_proxy(self):
        new_ip = self.apply_new_ip(num=1)

        if new_ip.get('code') == "SUCCESS":
            channel_server = new_ip.get('data', {})[0]["server"]
            deadline = new_ip.get('data', {})[0]["deadline"]

            return {
                "proxy_url": f"http://{self.auth_key}:{self.auth_password}@{channel_server}",
                "deadline": deadline
            }
        return None


if __name__ == '__main__':
    qingguoProxy = QIngGuo(auth_key="A1E0C6DF", auth_password="606E1E6F0601")

    print(qingguoProxy.query_proxy())
