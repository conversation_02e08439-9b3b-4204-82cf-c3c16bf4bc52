# FastAPI 接口测试指南

## 项目状态检查

✅ **FastAPI 配置正常**
- `start.py` - 启动文件正常
- `app/main.py` - 主应用配置正常
- `app/api/router.py` - 路由配置正常
- `app/api/controller/douyin_api.py` - 抖音API控制器正常

## 启动服务

### 方法1: 使用 start.py（推荐）

```bash
python start.py
```

### 方法2: 直接使用 uvicorn

```bash
uvicorn app.main:app --host 0.0.0.0 --port 80 --reload
```

### 启动成功标志

看到以下信息表示启动成功：
```
INFO:     Uvicorn running on http://0.0.0.0:80 (Press CTRL+C to quit)
INFO:     Started reloader process
INFO:     Started server process
INFO:     Waiting for application startup.
```

## 访问接口文档

启动服务后，可以通过以下URL访问API文档：

### Swagger UI 文档
```
http://localhost/docs
```

### ReDoc 文档
```
http://localhost/redoc
```

## 可用的API接口

### 1. 抖音 API (Douyin)

**基础路径**: `/api/douyin/web`

#### 获取单个视频数据
- **URL**: `GET /api/douyin/web/fetch_one_video`
- **参数**: 
  - `aweme_id` (string): 作品ID
- **示例**: 
  ```
  GET http://localhost/api/douyin/web/fetch_one_video?aweme_id=7372484719365098803
  ```

#### 获取用户信息
- **URL**: `GET /api/douyin/web/fetch_user_profile`
- **参数**: 
  - `sec_user_id` (string): 用户ID
- **示例**: 
  ```
  GET http://localhost/api/douyin/web/fetch_user_profile?sec_user_id=MS4wLjABAAAA...
  ```

#### 搜索视频
- **URL**: `GET /api/douyin/web/fetch_search_video`
- **参数**: 
  - `keyword` (string): 搜索关键词
  - `page` (int, 可选): 页码，默认1
  - `page_size` (int, 可选): 每页数量，默认20
- **示例**: 
  ```
  GET http://localhost/api/douyin/web/fetch_search_video?keyword=美食&page=1&page_size=10
  ```

### 2. B站 API (Bilibili)

**基础路径**: `/api/bilibili/web`

#### 获取单个视频数据
- **URL**: `GET /api/bilibili/web/fetch_one_video`
- **参数**: 
  - `bv_id` (string): 视频BV号
- **示例**: 
  ```
  GET http://localhost/api/bilibili/web/fetch_one_video?bv_id=BV1M1421t7hT
  ```

#### 搜索视频
- **URL**: `GET /api/bilibili/web/fetch_search_video`
- **参数**: 
  - `keyword` (string): 搜索关键词
  - `page` (int, 可选): 页码
- **示例**: 
  ```
  GET http://localhost/api/bilibili/web/fetch_search_video?keyword=编程&page=1
  ```

### 3. TikTok API

**基础路径**: `/api/tiktok/web` 或 `/api/tiktok/app`

#### 获取单个视频数据
- **URL**: `GET /api/tiktok/web/fetch_one_video`
- **参数**: 
  - `aweme_id` (string): 视频ID
- **示例**: 
  ```
  GET http://localhost/api/tiktok/web/fetch_one_video?aweme_id=7372484719365098803
  ```

### 4. 混合解析 API

**基础路径**: `/api/hybrid`

#### 解析视频链接
- **URL**: `POST /api/hybrid/fetch_one_video`
- **参数**: 
  - `url` (string): 视频链接
- **示例**: 
  ```
  POST http://localhost/api/hybrid/fetch_one_video
  Content-Type: application/json
  
  {
    "url": "https://www.douyin.com/video/7372484719365098803"
  }
  ```

## 测试方法

### 1. 使用浏览器测试

直接在浏览器中访问GET接口：
```
http://localhost/api/douyin/web/fetch_one_video?aweme_id=7372484719365098803
```

### 2. 使用 curl 测试

```bash
# GET 请求
curl "http://localhost/api/douyin/web/fetch_one_video?aweme_id=7372484719365098803"

# POST 请求
curl -X POST "http://localhost/api/hybrid/fetch_one_video" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://www.douyin.com/video/7372484719365098803"}'
```

### 3. 使用 Python requests 测试

```python
import requests

# GET 请求
response = requests.get(
    "http://localhost/api/douyin/web/fetch_one_video",
    params={"aweme_id": "7372484719365098803"}
)
print(response.json())

# POST 请求
response = requests.post(
    "http://localhost/api/hybrid/fetch_one_video",
    json={"url": "https://www.douyin.com/video/7372484719365098803"}
)
print(response.json())
```

### 4. 使用 Postman 测试

1. 创建新的请求
2. 设置请求方法（GET/POST）
3. 输入URL：`http://localhost/api/douyin/web/fetch_one_video`
4. 添加参数或JSON body
5. 发送请求

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "router": "/api/douyin/web/fetch_one_video",
  "data": {
    // 具体的数据内容
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "An error occurred.",
  "support": "",
  "time": "2025-06-28 18:00:00",
  "router": "/api/douyin/web/fetch_one_video",
  "params": {}
}
```

## 常见问题排查

### 1. 服务启动失败

**检查端口占用**:
```bash
# Windows
netstat -ano | findstr :80

# Linux/Mac
lsof -i :80
```

**更改端口**:
修改 `config.yaml` 中的 `Host_Port` 配置

### 2. 接口返回错误

**检查爬虫配置**:
- 确保代理配置正确
- 检查浏览器API密钥是否有效
- 验证网络连接

**查看日志**:
启动时添加详细日志：
```bash
python start.py --log-level debug
```

### 3. 代理相关问题

**检查代理状态**:
```python
from crawlers.spiders.douyin.web_crawler import DouyinWebCrawler
crawler = DouyinWebCrawler()
status = crawler.request_manager.get_proxy_status()
print(status)
```

## 性能优化建议

1. **使用Redis缓存**: 确保Redis服务运行正常
2. **代理池管理**: 配置多个代理提高成功率
3. **并发控制**: 避免过高的并发请求
4. **错误重试**: 实现自动重试机制

## 安全注意事项

1. **API密钥保护**: 不要在公开代码中暴露API密钥
2. **访问控制**: 生产环境建议添加认证机制
3. **请求限制**: 实现请求频率限制
4. **日志记录**: 记录关键操作日志

## 快速测试脚本

创建 `test_api.py` 文件：

```python
import requests
import json

BASE_URL = "http://localhost/api"

def test_douyin_video():
    url = f"{BASE_URL}/douyin/web/fetch_one_video"
    params = {"aweme_id": "7372484719365098803"}
    response = requests.get(url, params=params)
    print("抖音视频测试:", response.status_code)
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))

def test_bilibili_video():
    url = f"{BASE_URL}/bilibili/web/fetch_one_video"
    params = {"bv_id": "BV1M1421t7hT"}
    response = requests.get(url, params=params)
    print("B站视频测试:", response.status_code)
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_douyin_video()
    test_bilibili_video()
```

运行测试：
```bash
python test_api.py
```
