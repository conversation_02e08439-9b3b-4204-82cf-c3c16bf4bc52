# -*- coding: utf-8 -*-
# 初始化数据库配置 以及 redis 配置常量


from tools.config_helper import root_config

# mysql config
RELATION_DB_PWD = root_config["datasource"]["password"]
RELATION_DB_USER = root_config["datasource"]["username"]
RELATION_DB_HOST = root_config["datasource"]["url"]
RELATION_DB_PORT = root_config["datasource"]["port"]
RELATION_DB_NAME = root_config["datasource"]["database"]
RELATION_DB_CONN_LIMIT_MAX = root_config["datasource"]["max_connections"]
RELATION_DB_CONN_LIMIT_MIN = root_config["datasource"]["min_connections"]

# redis config
redis_config = root_config["redis"]

REDIS_DB_HOST = redis_config["host"]
REDIS_DB_PWD = redis_config["password"]
REDIS_DB_PORT = redis_config["port"]
REDIS_DB_NUM = redis_config["db"]

# cache type
CACHE_TYPE_REDIS = "redis"
CACHE_TYPE_MEMORY = "memory"

print(REDIS_DB_NUM)
