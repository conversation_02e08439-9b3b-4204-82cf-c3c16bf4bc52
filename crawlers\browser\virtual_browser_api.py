# -*- coding: utf-8 -*-
import asyncio
import time

import requests
from playwright.async_api import async_playwright, Playwright


class VirtualBrowserApi:
    """
    文档 https://www.kdocs.cn/l/cq4lGX94cYk1
    """
    BASE_URL = "http://localhost:9000"

    def __init__(self, api_key):
        self.api_key = api_key
        self.headers = {
            'Content-Type': 'application/json',
            'api-key': api_key
        }

    def query_browser_list(self, group_name: str = "默认分组") -> list:
        """
        查询浏览器列表
        """
        url = f"{self.BASE_URL}/api/getBrowserList"

        try:
            response = requests.post(url, headers=self.headers, json={"group": group_name}).json()

            if not response.get('success'):
                return []
            return response["data"]

        except Exception as e:
            print(f"Error: {e}")
            return []

    def add_browser(self, browser_name: str,
                    browser_group: str = "默认分组",
                    os: str = "Win 11",
                    chrome_version: int = 132) -> int:
        """
        添加虚拟浏览器
        """
        try:
            params = {
                "name": browser_name,
                "group": browser_group,
                "os": os,
                "chrome_version": chrome_version,
                "cookie": {
                    "mode": 0,
                    "value": "",
                    "jsonStr": "[]"
                },
                "homepage": {
                    "mode": 0,
                    "value": ""
                }
            }
            response = requests.post(
                f"{self.BASE_URL}/api/addBrowser",
                headers=self.headers,
                json=params
            ).json()
            if not response.get('success'):
                return -1
            return response["data"]["id"]
        except Exception as e:
            print(f"错误: {e}")

    def stop_browser(self, browser_id: int) -> bool:
        """
            关闭环境
        """
        url = f"{self.BASE_URL}/api/stopBrowser"
        try:
            response = requests.post(url, json={'id': browser_id}, headers=self.headers).json()
            return response.get('success')
        except Exception as e:
            print(f"错误: {e}")
            return False

    def del_browser(self, browser_id: int) -> bool:
        """
            删除环境
        """
        url = f"{self.BASE_URL}/api/deleteBrowser"
        try:
            response = requests.post(url, json={'id': browser_id}, headers=self.headers).json()
            return response.get('success')
        except Exception as e:
            print(f"错误: {e}")
            return False

    def launch_browser(self, browser_id) -> dict:
        """
        启动虚拟浏览器
        """
        try:
            response = requests.post(
                f"{self.BASE_URL}/api/launchBrowser",
                json={'id': browser_id},
                headers=self.headers).json()

            if not response.get('success'):
                return {}

            return response
        except Exception as err:
            print(f"错误: {err}")


async def apply_new_douyin_cookie(playwright: Playwright, api: VirtualBrowserApi, platform: str) -> str:
    # 通过API启动环境
    browser_id = api.add_browser(browser_name="douyin_uid")
    print(browser_id)

    response = api.launch_browser(browser_id=browser_id)
    print(response)

    chromium = playwright.chromium
    browser = await chromium.connect_over_cdp(f"http://localhost:{response['data']['debuggingPort']}")

    # 使用当前窗口
    context = browser.contexts[0]
    page = await context.new_page()

    if platform == "douyin":
        await page.goto('https://www.douyin.com/discover')
    elif platform == "tiktok":
        await page.goto('https://www.tiktok.com/discover')
    else:
        await page.goto('https://www.bilibili.com')

    time.sleep(2)
    cookie_in = await context.cookies()
    cookies_str = ";".join([f"{cookie.get('name')}={cookie.get('value')}" for cookie in cookie_in])
    print(cookies_str)

    cookie_dict = dict()
    for cookie in cookie_in:
        cookie_dict[cookie.get('name')] = cookie.get('value')
    print(cookie_dict)

    print("stop browser :{}")
    response = api.stop_browser(browser_id=browser_id)

    print(response)
    response = api.del_browser(browser_id=browser_id)
    print(f"del browser :{response}")

    return cookies_str


async def sync_douyin_cookie(browser_api: VirtualBrowserApi, platform: str) -> str:
    async with async_playwright() as playwright:
        return await apply_new_douyin_cookie(playwright=playwright, api=browser_api, platform= platform)


if __name__ == "__main__":
    api = VirtualBrowserApi(api_key="eRYQnY16tPOLq1vQjbKusOImowrvyCk8")
    asyncio.run(sync_douyin_cookie(browser_api=api, platform="douyin"))
