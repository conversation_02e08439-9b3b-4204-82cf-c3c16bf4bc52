# -*- coding: utf-8 -*-
from bit_api import *
import time
import asyncio
from playwright.async_api import async_playwright, Playwright


async def run(playwright: Playwright):
    # /browser/open 接口会返回 selenium使用的http地址，以及webdriver的path，直接使用即可
    browser_id = "e87ce96309c9413ab2224cb3af2a6bc5"  # 窗口ID从窗口配置界面中复制，或者api创建后返回
    res = openBrowser(browser_id)
    ws = res['data']['ws']
    print("ws address ==>>> ", ws)

    chromium = playwright.chromium
    browser = await chromium.connect_over_cdp(ws)

    default_context = browser.contexts[0]

    print('new page and goto baidu')
    await default_context.add_init_script(path="../lib/stealth.min.js")

    page = await default_context.new_page()
    await page.goto('https://www.douyin.com/')

    time.sleep(2)

    cookies = await default_context.cookies(urls=["https://www.douyin.com/"])

    cookies_str = ";".join([f"{cookie.get('name')}={cookie.get('value')}" for cookie in cookies])
    print(cookies_str)
    cookie_dict = dict()
    for cookie in cookies:
        cookie_dict[cookie.get('name')] = cookie.get('value')

    print(cookies)
    print('clsoe page and browser')
    await page.close()

    time.sleep(2)
    closeBrowser(browser_id)


async def main():
    async with async_playwright() as playwright:
        await run(playwright)


asyncio.run(main())
