# -*- coding: utf-8 -*-
# @Time : 2025/5/23 17:53
# <AUTHOR> cyf
# @File : dy_models.py
from pydantic import Field, BaseModel


class DySearchResult(BaseModel):
    """
    抖音搜索结果
    """

    note_id: str = Field(..., description="帖子ID")
    pass


class DyDetail:
    """
    抖音详情数据存储模型
    """

    pass


class DyUserProfile:
    """
    抖音用户详情
    """

    pass


class DyUserPost:
    """
    抖音用户发布作品数据
    """

    pass


model = DySearchResult(note_id="s")
