# -*- coding: utf-8 -*-
# @Time : 2025/5/23 17:58
# <AUTHOR> cyf
# @File : bliibili_models.py

from datetime import datetime

from pydantic import BaseModel, Field
from typing import List


class BilibiliSearchResult(BaseModel):
    """
    Bilibili 搜索结果内容
    """

    search_keyword: str = Field(default="", description="搜索关键词")
    content_id: str = Field(default="", description="视频ID (aid)")
    bvid: str = Field(default="", description="视频短ID (bvid)")
    content_url: str = Field(default="", description="视频落地链接 (arcurl)")
    content_type: str = Field(default="video", description="内容类型，固定为video")
    title: str = Field(default="", description="视频标题")
    description: str = Field(default="", description="视频描述")
    tags: List[str] = Field(default_factory=list, description="视频标签列表")
    typename: str = Field(default="", description="视频分类名称")
    author: str = Field(default="", description="作者昵称")
    user_id: str = Field(default="", description="作者ID (mid)")
    user_avatar: str = Field(default="", description="作者头像地址 (upic)")
    pubdate: str = Field(default="", description="发布时间，格式为YYYY-MM-DD HH:MM:SS")
    duration: str = Field(default="", description="视频时长，格式为MM:SS")
    play_count: int = Field(default=0, description="播放量")
    danmaku_count: int = Field(default=0, description="弹幕数量")
    favorite_count: int = Field(default=0, description="收藏数量")
    comment_count: int = Field(default=0, description="评论数量")
    like_count: int = Field(default=0, description="点赞数量")
    rank_score: int = Field(default=0, description="排名分数")
    rank_index: int = Field(default=0, description="排名索引")
    rank_offset: int = Field(default=0, description="排名偏移")
    cover_url: str = Field(default="", description="视频封面图片地址")
    crawl_time: str = Field(
        default="", description="爬取时间，格式为YYYY-MM-DD HH:MM:SS"
    )

    @classmethod
    def from_json(cls, json_data: dict, search_keyword: str) -> "BilibiliSearchResult":
        """
        将 JSON 数据转换为 BilibiliSearchResult 对象
        Args:
            search_keyword(str): 搜索关键词
            json_data (dict): 原始 JSON 数据
        Returns:
            BilibiliContent: 转换后的 Pydantic 对象
        Raises:
            KeyError: 如果缺少关键字段
            ValueError: 如果数据格式无效
        """
        try:
            # 处理字段映射和数据清洗
            content_data = {
                "search_keyword": search_keyword,
                "content_id": str(json_data.get("id", "")),
                "bvid": json_data.get("bvid", ""),
                "content_url": json_data.get("arcurl", ""),
                "content_type": json_data.get("type", "video"),
                "title": json_data.get("title", ""),
                "description": json_data.get("description", ""),
                "tags": (
                    json_data.get("tag", "").split(",") if json_data.get("tag") else []
                ),
                "typename": json_data.get("typename", ""),
                "author": json_data.get("author", ""),
                "user_id": str(json_data.get("mid", "")),
                "user_avatar": json_data.get("upic", ""),
                "pubdate": (
                    datetime.fromtimestamp(json_data["pubdate"]).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    if json_data.get("pubdate")
                    else ""
                ),
                "duration": json_data.get("duration", ""),
                "play_count": json_data.get("play", 0),
                "danmaku_count": json_data.get("danmaku", 0),
                "favorite_count": json_data.get("favorites", 0),
                "comment_count": json_data.get("review", 0),
                "like_count": json_data.get("like", 0),
                "rank_score": json_data.get("rank_score", 0),
                "rank_index": json_data.get("rank_index", 0),
                "rank_offset": json_data.get("rank_offset", 0),
                "cover_url": (
                    f"https:{json_data['pic']}"
                    if json_data.get("pic", "").startswith("//")
                    else json_data.get("pic", "")
                ),
                "crawl_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
            return cls(**content_data)
        except KeyError as e:
            raise KeyError(f"缺少必要字段: {e}")
        except ValueError as e:
            raise ValueError(f"数据格式错误: {e}")


class BilibiliVideoInfo(BaseModel):
    """
    视频信息详情信息
    """

    pass
