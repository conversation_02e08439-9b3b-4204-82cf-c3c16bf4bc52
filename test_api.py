# -*- coding: utf-8 -*-
# @Time : 2025/6/28 18:00
# <AUTHOR> Assistant
# @File : test_api.py

"""
FastAPI 接口测试脚本
"""

import requests
import json
import time
from typing import Dict, Any


class APITester:
    def __init__(self, base_url: str = "http://localhost/api"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 30

    def test_endpoint(self, method: str, endpoint: str, params: Dict = None, json_data: Dict = None) -> Dict[str, Any]:
        """
        测试API端点
        
        Args:
            method: HTTP方法 (GET, POST)
            endpoint: API端点
            params: GET参数
            json_data: POST JSON数据
            
        Returns:
            测试结果字典
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            start_time = time.time()
            
            if method.upper() == "GET":
                response = self.session.get(url, params=params)
            elif method.upper() == "POST":
                response = self.session.post(url, json=json_data)
            else:
                return {"success": False, "error": f"不支持的HTTP方法: {method}"}
            
            end_time = time.time()
            response_time = round((end_time - start_time) * 1000, 2)  # 毫秒
            
            result = {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": f"{response_time}ms",
                "url": url,
                "method": method.upper()
            }
            
            try:
                result["data"] = response.json()
            except:
                result["data"] = response.text
                
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url,
                "method": method.upper()
            }

    def print_result(self, test_name: str, result: Dict[str, Any]):
        """打印测试结果"""
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print(f"{'='*60}")
        
        if result["success"]:
            print(f"✅ 成功 - {result['method']} {result['url']}")
            print(f"⏱️  响应时间: {result['response_time']}")
            print(f"📊 状态码: {result['status_code']}")
            
            # 打印响应数据（限制长度）
            if isinstance(result["data"], dict):
                data_str = json.dumps(result["data"], indent=2, ensure_ascii=False)
                if len(data_str) > 500:
                    print(f"📄 响应数据: {data_str[:500]}...")
                else:
                    print(f"📄 响应数据: {data_str}")
            else:
                print(f"📄 响应数据: {str(result['data'])[:200]}...")
        else:
            print(f"❌ 失败 - {result['method']} {result.get('url', 'N/A')}")
            print(f"📊 状态码: {result.get('status_code', 'N/A')}")
            print(f"❗ 错误: {result.get('error', 'Unknown error')}")

    def test_douyin_apis(self):
        """测试抖音API"""
        print("\n🎵 开始测试抖音API...")
        
        # 测试获取单个视频
        result = self.test_endpoint(
            "GET", 
            "/douyin/web/fetch_one_video",
            params={"aweme_id": "7372484719365098803"}
        )
        self.print_result("抖音 - 获取单个视频", result)
        
        # 测试搜索视频
        result = self.test_endpoint(
            "GET",
            "/douyin/web/fetch_search_video", 
            params={"keyword": "美食", "page": 1, "page_size": 5}
        )
        self.print_result("抖音 - 搜索视频", result)

    def test_bilibili_apis(self):
        """测试B站API"""
        print("\n📺 开始测试B站API...")
        
        # 测试获取单个视频
        result = self.test_endpoint(
            "GET",
            "/bilibili/web/fetch_one_video",
            params={"bv_id": "BV1M1421t7hT"}
        )
        self.print_result("B站 - 获取单个视频", result)
        
        # 测试搜索视频
        result = self.test_endpoint(
            "GET",
            "/bilibili/web/fetch_search_video",
            params={"keyword": "编程", "page": 1}
        )
        self.print_result("B站 - 搜索视频", result)

    def test_hybrid_apis(self):
        """测试混合解析API"""
        print("\n🔄 开始测试混合解析API...")
        
        # 测试解析抖音链接
        result = self.test_endpoint(
            "POST",
            "/hybrid/fetch_one_video",
            json_data={"url": "https://www.douyin.com/video/7372484719365098803"}
        )
        self.print_result("混合解析 - 抖音链接", result)

    def test_server_status(self):
        """测试服务器状态"""
        print("\n🔍 检查服务器状态...")
        
        try:
            # 测试根路径
            response = self.session.get(self.base_url.replace("/api", ""), timeout=5)
            if response.status_code == 200:
                print("✅ 服务器运行正常")
                print(f"📍 服务地址: {self.base_url.replace('/api', '')}")
            else:
                print(f"⚠️  服务器响应异常: {response.status_code}")
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            print("💡 请确保服务已启动: python start.py")

    def test_docs_access(self):
        """测试文档访问"""
        print("\n📚 检查API文档...")
        
        docs_urls = [
            ("/docs", "Swagger UI"),
            ("/redoc", "ReDoc")
        ]
        
        for path, name in docs_urls:
            try:
                url = self.base_url.replace("/api", path)
                response = self.session.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {name} 可访问: {url}")
                else:
                    print(f"❌ {name} 访问失败: {response.status_code}")
            except Exception as e:
                print(f"❌ {name} 连接失败: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API测试...")
        print(f"🎯 目标服务器: {self.base_url}")
        
        # 检查服务器状态
        self.test_server_status()
        
        # 检查文档访问
        self.test_docs_access()
        
        # 测试各个API
        self.test_douyin_apis()
        self.test_bilibili_apis()
        self.test_hybrid_apis()
        
        print(f"\n{'='*60}")
        print("🎉 测试完成!")
        print("💡 如果有失败的测试，请检查:")
        print("   1. 服务是否正常启动")
        print("   2. 网络连接是否正常") 
        print("   3. 代理配置是否正确")
        print("   4. API密钥是否有效")


def main():
    """主函数"""
    print("FastAPI 接口测试工具")
    print("=" * 60)
    
    # 创建测试器
    tester = APITester()
    
    # 运行所有测试
    tester.run_all_tests()


if __name__ == "__main__":
    main()
