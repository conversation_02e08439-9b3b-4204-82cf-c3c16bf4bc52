# -*- coding: utf-8 -*-
# @Time : 2025/6/28 17:30
# <AUTHOR> Assistant
# @File : simple_test.py

"""
简化的Bilibili测试脚本，验证修复是否成功
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

def test_import():
    """测试导入是否成功"""
    try:
        print("1. 测试导入模块...")
        from crawlers.spiders.bilibili.web_crawler import BilibiliWebCrawler
        print("   ✓ BilibiliWebCrawler 导入成功")
        
        print("\n2. 测试初始化...")
        crawler = BilibiliWebCrawler()
        print("   ✓ BilibiliWebCrawler 初始化成功")
        
        print("\n3. 测试配置信息...")
        crawler.print_config_info()
        
        print("\n4. 测试RequestManager...")
        print(f"   RequestManager类型: {type(crawler.request_manager)}")
        print(f"   代理缓存类型: {type(crawler.request_manager.proxy_cache)}")
        print(f"   Cookie缓存类型: {type(crawler.request_manager.cookie_cache)}")
        
        print("\n✓ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_reading():
    """测试配置文件读取"""
    try:
        print("\n5. 测试配置文件读取...")
        import yaml
        
        config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        
        proxy_config = config.get("ProxyConfig", {})
        browser_config = config.get("VirtualBrowserConfig", {})
        cookie_config = config.get("CookieConfig", {})
        
        print(f"   ProxyConfig: {proxy_config}")
        print(f"   VirtualBrowserConfig: {browser_config}")
        print(f"   CookieConfig: {cookie_config}")
        print("   ✓ 配置文件读取成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件读取失败: {e}")
        return False

def test_api_controller():
    """测试API控制器"""
    try:
        print("\n6. 测试API控制器...")
        from app.api.controller.bilibili_api import BilibiliWebCrawler as api_crawler
        print("   ✓ API控制器导入成功")
        
        print(f"   爬虫类型: {type(api_crawler).__name__}")
        api_crawler.print_config_info()
        print("   ✓ API控制器测试成功")
        
        return True
        
    except Exception as e:
        print(f"✗ API控制器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始简化测试...\n")
    
    success = True
    success &= test_import()
    success &= test_config_reading()
    success &= test_api_controller()
    
    if success:
        print("\n🎉 所有测试都通过了！修复成功！")
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
    
    print("\n测试完成。")
