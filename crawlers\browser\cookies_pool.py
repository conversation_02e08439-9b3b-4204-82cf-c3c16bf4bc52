import asyncio
import time
import sys
import os
from playwright.async_api import async_playwright, Playwright
from virtual_browser_api import VirtualBrowserApi

# 添加proxy模块路径
# 获取当前文件的目录路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录路径（假设fingerprintBrowser目录在项目根目录下）
root_dir = os.path.dirname(current_dir)
# 将根目录添加到Python路径中
if root_dir not in sys.path:
    sys.path.append(root_dir)

from crawlers.proxy.qing_guo import QIngGuo


class CookiesPool:
    def __init__(self, api_key):
        self.api_key = api_key
        self.browser_api = VirtualBrowserApi(api_key=api_key)
        self.platform_urls = {
            "douyin": "https://www.douyin.com/discover",
            "bilibili": "https://www.bilibili.com",
            "tiktok": "https://www.tiktok.com",
        }

    async def apply_new_cookie(
        self, platform, use_proxy=False, proxy_auth_key=None, proxy_auth_password=None
    ):
        """申请新的cookie，支持抖音、bilibili、tiktok平台

        Args:
            platform: 平台名称，支持'douyin', 'bilibili', 'tiktok'
            use_proxy: 是否使用代理
            proxy_auth_key: 青果代理验证密钥
            proxy_auth_password: 青果代理验证密码
        """
        if platform not in self.platform_urls:
            raise ValueError(
                f"不支持的平台: {platform}，可选项: {list(self.platform_urls.keys())}"
            )

        # 初始化代理
        proxy_url = None
        if use_proxy:
            if not proxy_auth_key or not proxy_auth_password:
                raise ValueError("使用代理时需要提供代理验证密钥和密码")

            proxy_client = QIngGuo(
                auth_key=proxy_auth_key, auth_password=proxy_auth_password
            )
            proxy_url = proxy_client.query_proxy()
            if not proxy_url:
                print("获取代理失败，将不使用代理")
            else:
                print(f"成功获取代理: {proxy_url}")

        async with async_playwright() as playwright:
            return await self._get_platform_cookie(playwright, platform, proxy_url)

    async def _get_platform_cookie(
        self, playwright: Playwright, platform: str, proxy_url: str = None
    ) -> str:
        """获取指定平台的cookie

        Args:
            playwright: Playwright实例
            platform: 平台名称
            proxy_url: 代理URL地址，格式为 *****************************:port
        """
        # 通过API启动环境
        browser_id = self.browser_api.add_browser(browser_name=f"{platform}_uid")
        print(f"创建浏览器ID: {browser_id}")

        response = self.browser_api.launch_browser(browser_id=browser_id)
        if not response:
            print(f"启动浏览器失败")
            return ""

        try:
            chromium = playwright.chromium
            browser = await chromium.connect_over_cdp(
                f"http://localhost:{response['data']['debuggingPort']}"
            )

            # 创建上下文参数，如果有代理，添加代理设置
            context_options = {}
            if proxy_url:
                context_options["proxy"] = {"server": proxy_url}

            # context = browser.contexts[0]
            # 关闭现有上下文并创建新的有代理的上下文
            # if browser.contexts:
            #     for ctx in browser.contexts:
            #         await ctx.close()
            #
            # 创建新的上下文，并应用代理设置
            context = await browser.new_context(**context_options)
            # 关闭上下文
            page = await context.new_page()

            # 访问对应平台的URL
            await page.goto(self.platform_urls[platform])

            # 等待页面加载和cookie生成
            time.sleep(2)

            # 获取cookie
            cookie_in = await context.cookies()
            cookies_str = ";".join(
                [f"{cookie.get('name')}={cookie.get('value')}" for cookie in cookie_in]
            )

            # 记录详细cookie信息
            cookie_dict = {
                cookie.get("name"): cookie.get("value") for cookie in cookie_in
            }
            print(f"{platform} cookie获取成功")

            return cookies_str

        except Exception as e:
            print(f"获取{platform} cookie时出错: {e}")
            return ""

        finally:
            # 关闭并删除浏览器环境
            print(f"关闭浏览器: {browser_id}")
            self.browser_api.stop_browser(browser_id=browser_id)
            self.browser_api.del_browser(browser_id=browser_id)

    def get_cookie(
        self, platform, use_proxy=False, proxy_auth_key=None, proxy_auth_password=None
    ):
        """同步方法获取指定平台的cookie

        Args:
            platform: 平台名称，支持'douyin', 'bilibili', 'tiktok'
            use_proxy: 是否使用代理
            proxy_auth_key: 青果代理验证密钥
            proxy_auth_password: 青果代理验证密码
        """
        return asyncio.run(
            self.apply_new_cookie(
                platform, use_proxy, proxy_auth_key, proxy_auth_password
            )
        )


if __name__ == "__main__":
    # 测试代码
    pool = CookiesPool(api_key="CJMZeL7iaMRpTIKWn44e2dmaw0jNaOQI")

    # 不使用代理获取抖音的cookie
    douyin_cookie = pool.get_cookie("tiktok")
    print(f"抖音Cookie: {douyin_cookie}")

    # 使用代理获取抖音的cookie
    # douyin_cookie_with_proxy = pool.get_cookie(
    #     "douyin",
    #     use_proxy=True,
    #     proxy_auth_key="CDBD5AC2",
    #     proxy_auth_password="C1C2977F6BC4",
    # )
    # print(f"代理抖音Cookie: {douyin_cookie_with_proxy}")
