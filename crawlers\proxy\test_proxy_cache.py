# -*- coding: utf-8 -*-
# @Time : 2025/6/28 16:00
# <AUTHOR> Assistant
# @File : test_proxy_cache.py

"""
代理缓存测试文件
"""

import asyncio
import time
from crawlers.proxy.proxy_cache import ProxyCache
from crawlers.utils.request_manager import RequestManager


def test_proxy_cache_basic():
    """
    测试代理缓存基本功能
    """
    print("=== 测试代理缓存基本功能 ===")
    
    # 初始化代理缓存
    proxy_cache = ProxyCache(
        auth_key="A1E0C6DF",
        auth_password="606E1E6F0601",
        proxy_refresh_interval=1,  # 1分钟过期
        cache_type="memory"  # 使用内存缓存进行测试
    )
    
    # 测试获取代理
    print("1. 获取代理:")
    proxy_info = proxy_cache.get_proxy()
    if proxy_info:
        print(f"   代理URL: {proxy_info['proxy_url']}")
        print(f"   过期时间: {proxy_info['deadline']}")
    else:
        print("   获取代理失败")
    
    # 测试再次获取（应该从缓存返回）
    print("\n2. 再次获取代理（从缓存）:")
    proxy_info2 = proxy_cache.get_proxy()
    if proxy_info2:
        print(f"   代理URL: {proxy_info2['proxy_url']}")
        print(f"   是否相同: {proxy_info['proxy_url'] == proxy_info2['proxy_url'] if proxy_info else False}")
    
    # 测试代理状态
    print("\n3. 代理状态:")
    status = proxy_cache.get_proxy_status()
    print(f"   状态: {status}")
    
    # 测试标记失败
    print("\n4. 标记失败:")
    proxy_cache.mark_failed()
    proxy_cache.mark_failed()
    status = proxy_cache.get_proxy_status()
    print(f"   失败次数: {status.get('failed_count', 0)}")
    
    # 测试强制刷新
    print("\n5. 强制刷新:")
    new_proxy = proxy_cache.force_refresh()
    if new_proxy:
        print(f"   新代理URL: {new_proxy['proxy_url']}")
    else:
        print("   强制刷新失败")


def test_request_manager_integration():
    """
    测试RequestManager集成代理缓存
    """
    print("\n=== 测试RequestManager集成 ===")
    
    # 初始化RequestManager
    request_manager = RequestManager(
        browser_api_key="test_browser_key",
        proxy_auth_key="A1E0C6DF",
        proxy_auth_password="606E1E6F0601",
        cookie_refresh_interval=30,
        proxy_refresh_interval=1,
        cache_type="memory"
    )
    
    # 测试获取代理
    print("1. 通过RequestManager获取代理:")
    proxy_url = request_manager.get_proxy()
    if proxy_url:
        print(f"   代理URL: {proxy_url}")
    else:
        print("   获取代理失败")
    
    # 测试获取完整代理信息
    print("\n2. 获取完整代理信息:")
    proxy_info = request_manager.get_proxy_info()
    if proxy_info:
        print(f"   代理URL: {proxy_info['proxy_url']}")
        print(f"   过期时间: {proxy_info['deadline']}")
    else:
        print("   获取代理信息失败")
    
    # 测试代理状态
    print("\n3. 代理状态:")
    status = request_manager.get_proxy_status()
    print(f"   状态: {status}")
    
    # 测试标记请求失败
    print("\n4. 标记请求失败:")
    request_manager.mark_request_failed("douyin")
    status = request_manager.get_proxy_status()
    print(f"   失败次数: {status.get('failed_count', 0)}")
    
    # 测试标记请求成功
    print("\n5. 标记请求成功:")
    request_manager.mark_request_success("douyin")
    status = request_manager.get_proxy_status()
    print(f"   失败次数: {status.get('failed_count', 0)}")
    
    # 测试强制刷新代理
    print("\n6. 强制刷新代理:")
    new_proxy_url = request_manager.force_refresh_proxy()
    if new_proxy_url:
        print(f"   新代理URL: {new_proxy_url}")
    else:
        print("   强制刷新失败")


async def test_prepare_request():
    """
    测试准备请求功能
    """
    print("\n=== 测试准备请求功能 ===")
    
    request_manager = RequestManager(
        browser_api_key="test_browser_key",
        proxy_auth_key="A1E0C6DF",
        proxy_auth_password="606E1E6F0601",
        cache_type="memory"
    )
    
    # 测试准备请求
    print("1. 准备请求:")
    try:
        cookie, proxy = await request_manager.prepare_request("douyin")
        print(f"   Cookie: {cookie[:50] + '...' if cookie and len(cookie) > 50 else cookie}")
        print(f"   Proxy: {proxy}")
    except Exception as e:
        print(f"   准备请求失败: {e}")


def simulate_crawler_workflow():
    """
    模拟爬虫工作流程
    """
    print("\n=== 模拟爬虫工作流程 ===")
    
    request_manager = RequestManager(
        browser_api_key="test_browser_key",
        proxy_auth_key="A1E0C6DF",
        proxy_auth_password="606E1E6F0601",
        cache_type="memory"
    )
    
    # 模拟多次请求
    for i in range(5):
        print(f"\n第{i+1}次请求:")
        
        # 获取代理
        proxy_url = request_manager.get_proxy()
        if not proxy_url:
            print("   无法获取代理，跳过请求")
            continue
        
        print(f"   使用代理: {proxy_url}")
        
        # 模拟请求结果
        import random
        success = random.choice([True, True, True, False])  # 75%成功率
        
        if success:
            print("   请求成功")
            request_manager.mark_request_success("douyin")
        else:
            print("   请求失败")
            request_manager.mark_request_failed("douyin")
        
        # 显示当前状态
        status = request_manager.get_proxy_status()
        print(f"   当前失败次数: {status.get('failed_count', 0)}")


if __name__ == "__main__":
    try:
        # 运行基本测试
        test_proxy_cache_basic()
        
        # 运行RequestManager集成测试
        test_request_manager_integration()
        
        # 运行异步测试
        asyncio.run(test_prepare_request())
        
        # 模拟爬虫工作流程
        simulate_crawler_workflow()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
