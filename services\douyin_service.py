import asyncio
from typing import List, Dict
from crawlers.spiders.douyin.web_crawler import DouyinWebCrawler


class DyService:
    """
    抖音服务
    """

    def __init__(self, crawler_configs: List[Dict] = None):
        """
        初始化抖音服务
        Args:
            crawler_configs: 爬虫配置列表，每个配置包含:
                {
                    "browser_api_key": str,
                    "proxy_auth_key": str,
                    "proxy_auth_password": str,
                    "cookie": str,  # 可选，特定cookie
                    "proxy": str,   # 可选，特定代理地址
                }
        """
        # 默认配置
        default_configs = [
            {
                "browser_api_key": "CJMZeL7iaMRpTIKWn44e2dmaw0jNaOQI",
                "proxy_auth_key": f"C1251FAB_{i}",
                "proxy_auth_password": "AF302A8D3FC1",
            }
            for i in range(3)  # 默认创建3个爬虫实例
        ]

        # 使用传入的配置或默认配置
        self.crawler_configs = crawler_configs or default_configs

        # 创建多个爬虫实例，每个实例使用独立的RequestManager
        self.crawlers = []
        for config in self.crawler_configs:
            # 创建爬虫实例（RequestManager会自动创建）
            crawler = DouyinWebCrawler(
                browser_api_key=config["browser_api_key"],
                proxy_auth_key=config["proxy_auth_key"],
                proxy_auth_password=config["proxy_auth_password"],
            )

            # 如果配置中指定了cookie和proxy，可以通过其他方式设置
            # 注意：新的代理缓存机制会自动管理代理，通常不需要手动设置
            if "cookie" in config:
                # 可以通过cookie_cache手动添加cookie
                crawler.request_manager.cookie_cache.add_cookie(
                    "douyin", config["cookie"]
                )

            # 代理现在通过代理缓存自动管理，不需要手动设置

            self.crawlers.append(crawler)

    async def search_video(
        self,
        keyword: str,
        offset: int = 0,
        sort_type: int = None,
        publish_time: int = None,
    ):
        """
        并发搜索抖音视频,使用预创建的多个爬虫实例并行搜索，返回最快的结果
        Args:
            keyword: 搜索关键词
            offset: 偏移量,默认0
            sort_type: 排序类型
            publish_time: 发布时间过滤
        Returns:
            第一个成功返回的结果
        """

        # 定义搜索任务
        async def search_task(crawler):
            try:
                return await crawler.fetch_search_video(
                    keyword=keyword,
                    offset=offset,
                    sort_type=sort_type,
                    publish_time=publish_time,
                )
            except Exception as e:
                print(f"搜索失败: {e}")
                return None

        # 并发执行搜索任务，使用预创建的爬虫实例
        tasks = [search_task(crawler) for crawler in self.crawlers]

        # 使用as_completed获取最快返回的有效结果
        for future in asyncio.as_completed(tasks):
            result = await future
            if result:  # 如果结果有效就立即返回
                return result

        return None  # 所有任务都失败则返回None


if __name__ == "__main__":
    pass
