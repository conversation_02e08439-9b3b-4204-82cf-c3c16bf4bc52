Web:
  PyWebIO_Enable: true
  # APP Information
  Domain: https://douyin.wtf

  # APP Configuration
  PyWebIO_Theme: minty
  Max_Take_URLs: 30


  # Web Information
  Tab_Title: 抖音/B站    # Web title | Web标题
  Description: 抖音B站，抓取数据服务   # Web description | Web描述
  Favicon: https://raw.githubusercontent.com/Evil0ctal/Douyin_TikTok_Download_API/main/logo/logo192.png    # Web favicon | Web图标

  Easter_Egg: true
  Live2D_Enable: true
  Live2D_JS: https://fastly.jsdelivr.net/gh/TikHubIO/TikHub_live2d@latest/autoload.js

# API
API:
  # Network Configuration
  Host_IP: 0.0.0.0    # default IP | 默认IP
  Host_Port: 80    # default port is 80 | 默认端口为80
  Docs_URL: /docs    # API documentation URL | API文档URL
  Redoc_URL: /redoc    # API documentation URL | API文档URL

  # API Information
  Version: V4.1.2    # API version | API版本
  Update_Time: 2025/03/16    # API update time | API更新时间
  Environment: Demo    # API environment | API环境

  # Download Configuration
  Download_Switch: true    # Enable download function | 启用下载功能

  # File Configuration
  Download_Path: "./download"    # Default download directory | 默认下载目录
  Download_File_Prefix: "douyin.wtf_"    # Default download file prefix | 默认下载文件前缀

browser:
  browser_api_key: eRYQnY16tPOLq1vQjbKusOImowrvyCk8

# 代理配置 60s
proxy:
  proxy_auth_key: C1251FAB
  proxy_auth_password: AF302A8D3FC1
  proxy_timeout: 60

# cookie setting interval 30 min
cookieSetting:
  cookie_refresh_interval: 30

datasource:
  url: rm-f8zc8983uf3z2vpk7zo.mysql.rds.aliyuncs.com
  username: data
  password: '@test-data-plus@^^123'
  database: test_core
  port: 3306
  max_connections: 10
  min_connections: 1

redis:
  host: r-f8zhjcd0pr7otfwzw4pd.redis.rds.aliyuncs.com
  port: 6379
  password: '@test-data-plus@^*123'
  db: 5

log:
  log_dir: F://py//media-crawler-anon//log

