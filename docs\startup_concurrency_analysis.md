# FastAPI启动时的并发问题分析与解决方案

## 问题分析

### 1. 全局爬虫实例的并发问题

**问题描述**：
在 `app/api/controller/douyin_api.py` 中，使用了模块级别的全局爬虫实例：

```python
# 使用配置文件默认值初始化爬虫
crawler = DouyinWebCrawler()
```

**风险**：
- 在FastAPI的多线程环境中，多个请求可能同时访问同一个爬虫实例
- 可能导致RequestManager状态混乱
- 共享的cookie和proxy缓存可能出现竞争条件

**解决方案**：
改为函数式创建爬虫实例：

```python
def get_crawler():
    """获取爬虫实例，避免全局实例的并发问题"""
    return DouyinWebCrawler()
```

### 2. Cookie生成的并发竞争

**问题描述**：
当Redis中没有有效cookie时，多个并发请求可能同时触发 `_generate_new_cookie`：

```python
async def get_cookie(self, platform: str) -> str:
    cookie = self.cookie_cache.get_cookie(platform)
    if cookie:
        return cookie
    # 多个请求可能同时执行到这里
    cookie = await self._generate_new_cookie(platform)
```

**风险**：
- 多个playwright实例同时创建虚拟浏览器
- 资源浪费和端口冲突
- 虚拟浏览器API可能无法处理并发请求

**解决方案**：
实现基于平台的锁机制：

```python
# 类级别的锁，用于防止并发cookie生成
_cookie_generation_locks = {}
_lock_creation_lock = asyncio.Lock()

async def get_cookie(self, platform: str) -> str:
    cookie = self.cookie_cache.get_cookie(platform)
    if cookie:
        return cookie

    # 使用锁防止并发生成cookie
    async with await self._get_cookie_lock(platform):
        # 再次检查缓存，可能在等待锁的过程中其他线程已经生成了cookie
        cookie = self.cookie_cache.get_cookie(platform)
        if cookie:
            return cookie
        
        # 从浏览器获取新cookie
        cookie = await self._generate_new_cookie(platform)
        if cookie:
            self.cookie_cache.add_cookie(platform, cookie)
        return cookie
```

### 3. 虚拟浏览器API的资源竞争

**问题描述**：
虚拟浏览器API在处理并发请求时可能出现：
- 端口冲突
- 浏览器实例创建失败
- 资源清理不及时

**解决方案**：
- 实现cookie生成锁机制（已实现）
- 添加重试机制
- 改进错误处理和资源清理

### 4. 启动时的服务依赖检查

**问题描述**：
应用启动时没有检查关键服务的可用性：
- Redis连接状态
- 虚拟浏览器API状态
- Cookie缓存状态

**解决方案**：
实现启动检查机制：

```python
class StartupChecker:
    async def check_redis_connection(self) -> bool:
        # 检查Redis连接
    
    async def check_virtual_browser_api(self) -> bool:
        # 检查虚拟浏览器API
    
    async def preload_cookies(self) -> Dict[str, bool]:
        # 检查cookie缓存状态
```

## 实施的解决方案

### 1. 修改RequestManager添加锁机制

- 添加了类级别的cookie生成锁
- 实现了双重检查锁定模式
- 防止并发cookie生成

### 2. 修改douyin_api.py避免全局实例

- 移除全局爬虫实例
- 改为函数式创建实例
- 避免共享状态问题

### 3. 添加启动检查机制

- 创建了 `app/startup_checks.py`
- 实现了Redis、虚拟浏览器API等关键服务检查
- 添加了健康检查接口

### 4. 改进FastAPI启动流程

- 使用新的lifespan事件处理
- 在启动时执行必要的检查
- 提供健康检查和就绪检查接口

## 建议的进一步优化

### 1. 连接池管理

考虑为虚拟浏览器API实现连接池：

```python
class BrowserPool:
    def __init__(self, max_browsers=5):
        self.max_browsers = max_browsers
        self.available_browsers = asyncio.Queue()
        self.active_browsers = set()
    
    async def get_browser(self):
        # 获取可用浏览器实例
    
    async def return_browser(self, browser_id):
        # 归还浏览器实例
```

### 2. 缓存预热

在应用启动时预生成常用平台的cookie：

```python
async def preload_cookies():
    platforms = ["douyin", "tiktok", "bilibili"]
    for platform in platforms:
        try:
            await request_manager.get_cookie(platform)
        except Exception as e:
            logger.warning(f"预加载{platform} cookie失败: {e}")
```

### 3. 监控和告警

添加关键指标监控：
- Cookie生成成功率
- 虚拟浏览器API响应时间
- 并发请求数量
- 错误率统计

### 4. 优雅降级

当虚拟浏览器API不可用时：
- 使用备用cookie
- 降级到无cookie模式
- 返回友好的错误信息

## 测试建议

### 1. 并发测试

使用工具如 `locust` 或 `ab` 进行并发测试：

```bash
# 测试并发cookie生成
ab -n 100 -c 10 http://localhost:8000/api/douyin/web/fetch_one_video?aweme_id=7372484719365098803
```

### 2. 启动测试

测试不同启动条件下的应用行为：
- Redis不可用
- 虚拟浏览器API不可用
- 网络延迟较高

### 3. 压力测试

测试高并发情况下的系统稳定性：
- 大量并发请求
- 长时间运行测试
- 内存和CPU使用监控

## 总结

通过实施上述解决方案，可以有效解决FastAPI启动时Redis中没有cookie需要使用playwright创建新cookie时的进程调度问题。主要改进包括：

1. **并发控制**：通过锁机制防止并发cookie生成
2. **实例隔离**：避免全局实例的共享状态问题
3. **启动检查**：确保关键服务可用性
4. **健康监控**：提供实时状态检查接口

这些改进将显著提高系统的稳定性和可靠性，特别是在高并发场景下。
