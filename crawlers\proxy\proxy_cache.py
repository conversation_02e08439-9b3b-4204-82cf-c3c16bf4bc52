# -*- coding: utf-8 -*-
# @Time : 2025/6/28 16:00
# <AUTHOR> Assistant
# @File : proxy_cache.py

import json
from datetime import timedelta, datetime
from typing import Optional, Dict

from crawlers.cache.cache_factory import CacheFactory
from crawlers.proxy.qing_guo import QIngGuo


class ProxyCache:
    def __init__(self, auth_key: str, auth_password: str, proxy_refresh_interval: int = 1, cache_type: str = "redis"):
        """
        初始化IP代理缓存管理器

        Args:
            auth_key: 代理认证密钥
            auth_password: 代理认证密码
            proxy_refresh_interval: 代理刷新间隔(分钟),默认1分钟
            cache_type: 缓存类型，支持 'memory' 或 'redis'
        """
        self.cache = CacheFactory.create_cache(cache_type, cron_interval=10)
        self.refresh_interval = timedelta(minutes=proxy_refresh_interval)
        self.qing_guo_client = QIngGuo(auth_key=auth_key, auth_password=auth_password)
        self.cache_key = "proxy:ip_proxy"  # 固定的缓存key，因为IP代理没有platform概念

    def add_proxy(self, proxy_info: Dict) -> None:
        """
        添加或更新代理信息

        Args:
            proxy_info: 代理信息字典，包含proxy_url和deadline
        """
        proxy_cache_info = {
            "proxy_url": proxy_info["proxy_url"],
            "deadline": proxy_info["deadline"],
            "failed_count": 0,
            "last_refresh": datetime.now().isoformat(),
        }
        # 缓存1分钟
        self.cache.set(self.cache_key, json.dumps(proxy_cache_info), 60)

    def get_proxy(self) -> Optional[Dict]:
        """
        获取可用的代理信息，如果没有或过期则自动获取新的

        Returns:
            如果代理有效返回代理信息字典,否则返回None
        """
        cached_data = self.cache.get(self.cache_key)
        
        # 如果缓存中没有数据，获取新的代理
        if not cached_data:
            return self._fetch_new_proxy()

        try:
            proxy_info = json.loads(cached_data)
        except (json.JSONDecodeError, TypeError):
            return self._fetch_new_proxy()

        # 检查失败次数
        if proxy_info.get("failed_count", 0) >= 3:
            self.cache.set(self.cache_key, "", 0)  # 立即过期
            return self._fetch_new_proxy()

        # 检查是否需要刷新（1分钟过期）
        last_refresh = datetime.fromisoformat(proxy_info["last_refresh"])
        if datetime.now() - last_refresh > self.refresh_interval:
            self.cache.set(self.cache_key, "", 0)  # 立即过期
            return self._fetch_new_proxy()

        # 检查deadline是否过期
        try:
            deadline = datetime.strptime(proxy_info["deadline"], "%Y-%m-%d %H:%M:%S")
            if datetime.now() >= deadline:
                self.cache.set(self.cache_key, "", 0)  # 立即过期
                return self._fetch_new_proxy()
        except (ValueError, TypeError):
            return self._fetch_new_proxy()

        return {
            "proxy_url": proxy_info["proxy_url"],
            "deadline": proxy_info["deadline"]
        }

    def _fetch_new_proxy(self) -> Optional[Dict]:
        """
        从青果代理获取新的代理信息

        Returns:
            代理信息字典或None
        """
        try:
            proxy_info = self.qing_guo_client.query_proxy()
            if proxy_info:
                self.add_proxy(proxy_info)
                return {
                    "proxy_url": proxy_info["proxy_url"],
                    "deadline": proxy_info["deadline"]
                }
        except Exception as e:
            print(f"获取新代理失败: {e}")
        
        return None

    def mark_failed(self) -> None:
        """
        标记当前代理请求失败
        """
        cached_data = self.cache.get(self.cache_key)
        if cached_data:
            try:
                proxy_info = json.loads(cached_data)
                proxy_info["failed_count"] = proxy_info.get("failed_count", 0) + 1
                self.cache.set(self.cache_key, json.dumps(proxy_info), 60)
            except (json.JSONDecodeError, TypeError):
                pass

    def clear_failed_count(self) -> None:
        """
        清除代理失败次数
        """
        cached_data = self.cache.get(self.cache_key)
        if cached_data:
            try:
                proxy_info = json.loads(cached_data)
                proxy_info["failed_count"] = 0
                self.cache.set(self.cache_key, json.dumps(proxy_info), 60)
            except (json.JSONDecodeError, TypeError):
                pass

    def force_refresh(self) -> Optional[Dict]:
        """
        强制刷新代理，立即获取新的代理信息

        Returns:
            新的代理信息字典或None
        """
        self.cache.set(self.cache_key, "", 0)  # 立即过期当前缓存
        return self._fetch_new_proxy()

    def get_proxy_status(self) -> Dict:
        """
        获取当前代理状态信息

        Returns:
            包含代理状态的字典
        """
        cached_data = self.cache.get(self.cache_key)
        if not cached_data:
            return {"status": "no_proxy", "message": "没有缓存的代理"}

        try:
            proxy_info = json.loads(cached_data)
            last_refresh = datetime.fromisoformat(proxy_info["last_refresh"])
            time_since_refresh = datetime.now() - last_refresh
            
            return {
                "status": "active",
                "proxy_url": proxy_info["proxy_url"],
                "deadline": proxy_info["deadline"],
                "failed_count": proxy_info.get("failed_count", 0),
                "time_since_refresh": str(time_since_refresh),
                "expires_in": str(self.refresh_interval - time_since_refresh) if time_since_refresh < self.refresh_interval else "expired"
            }
        except (json.JSONDecodeError, TypeError, ValueError):
            return {"status": "error", "message": "代理信息解析失败"}
