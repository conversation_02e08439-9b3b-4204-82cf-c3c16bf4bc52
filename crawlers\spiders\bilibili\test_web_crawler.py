# -*- coding: utf-8 -*-
# @Time : 2025/6/28 17:30
# <AUTHOR> Assistant
# @File : test_web_crawler.py

"""
测试修复后的BilibiliWebCrawler
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from crawlers.spiders.bilibili.web_crawler import BilibiliWebCrawler


def test_crawler_initialization():
    """
    测试爬虫初始化
    """
    print("=== 测试BilibiliWebCrawler初始化 ===\n")
    
    # 1. 使用配置文件默认值初始化
    print("1. 使用配置文件默认值初始化:")
    crawler1 = BilibiliWebCrawler()
    crawler1.print_config_info()
    
    # 2. 覆盖部分参数初始化
    print("\n2. 覆盖部分参数初始化:")
    crawler2 = BilibiliWebCrawler(
        browser_api_key="custom_browser_key",
        proxy_auth_key="custom_proxy_key",
        cookie_refresh_interval=60
    )
    crawler2.print_config_info()
    
    # 3. 完全自定义参数初始化
    print("\n3. 完全自定义参数初始化:")
    crawler3 = BilibiliWebCrawler(
        browser_api_key="test_browser_key",
        proxy_auth_key="test_proxy_key",
        proxy_auth_password="test_proxy_password",
        cookie_refresh_interval=45,
        proxy_refresh_interval=2,
        cache_type="memory"
    )
    crawler3.print_config_info()


def test_request_manager_integration():
    """
    测试RequestManager集成
    """
    print("\n=== 测试RequestManager集成 ===\n")
    
    crawler = BilibiliWebCrawler()
    
    # 测试RequestManager是否正确初始化
    print("1. RequestManager初始化状态:")
    print(f"   RequestManager类型: {type(crawler.request_manager)}")
    print(f"   代理缓存类型: {type(crawler.request_manager.proxy_cache)}")
    print(f"   Cookie缓存类型: {type(crawler.request_manager.cookie_cache)}")
    
    # 测试代理状态
    print("\n2. 代理状态:")
    try:
        proxy_status = crawler.request_manager.get_proxy_status()
        print(f"   代理状态: {proxy_status}")
    except Exception as e:
        print(f"   获取代理状态失败: {e}")
    
    # 测试获取代理
    print("\n3. 获取代理:")
    try:
        proxy_url = crawler.request_manager.get_proxy()
        if proxy_url:
            print(f"   代理URL: {proxy_url}")
        else:
            print("   未获取到代理")
    except Exception as e:
        print(f"   获取代理失败: {e}")


async def test_crawler_methods():
    """
    测试爬虫方法
    """
    print("\n=== 测试爬虫方法 ===\n")
    
    crawler = BilibiliWebCrawler()
    
    # 测试prepare_request方法
    print("1. 测试prepare_request方法:")
    try:
        cookie, proxy = await crawler.request_manager.prepare_request("bilibili")
        print(f"   Cookie: {cookie[:50] + '...' if cookie and len(cookie) > 50 else cookie}")
        print(f"   Proxy: {proxy}")
    except Exception as e:
        print(f"   prepare_request失败: {e}")
    
    # 测试get_bilibili_headers方法
    print("\n2. 测试get_bilibili_headers方法:")
    try:
        headers_kwargs = await crawler.get_bilibili_headers()
        print(f"   Headers keys: {list(headers_kwargs.get('headers', {}).keys())}")
        print(f"   Proxies: {headers_kwargs.get('proxies')}")
    except Exception as e:
        print(f"   get_bilibili_headers失败: {e}")


def test_config_file_reading():
    """
    测试配置文件读取
    """
    print("\n=== 测试配置文件读取 ===\n")
    
    # 导入配置
    import yaml
    
    config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    
    print("1. 配置文件内容:")
    print(f"   ProxyConfig: {config.get('ProxyConfig', {})}")
    print(f"   VirtualBrowserConfig: {config.get('VirtualBrowserConfig', {})}")
    print(f"   CookieConfig: {config.get('CookieConfig', {})}")
    
    print("\n2. 验证配置读取:")
    crawler = BilibiliWebCrawler()
    config_info = crawler.get_config_info()
    
    proxy_config = config.get("ProxyConfig", {})
    browser_config = config.get("VirtualBrowserConfig", {})
    cookie_config = config.get("CookieConfig", {})
    
    print(f"   配置文件proxy_auth_key: {proxy_config.get('proxy_auth_key')}")
    print(f"   爬虫实例proxy_auth_key: {crawler.proxy_auth_key}")
    print(f"   配置文件browser_api_key: {browser_config.get('browser_api_key')}")
    print(f"   爬虫实例browser_api_key: {crawler.browser_api_key}")


def test_error_handling():
    """
    测试错误处理
    """
    print("\n=== 测试错误处理 ===\n")
    
    # 测试空配置初始化
    print("1. 测试空配置初始化:")
    try:
        crawler = BilibiliWebCrawler(
            browser_api_key="",
            proxy_auth_key="",
            proxy_auth_password=""
        )
        crawler.print_config_info()
        print("   空配置初始化成功")
    except Exception as e:
        print(f"   空配置初始化失败: {e}")
    
    # 测试无效参数
    print("\n2. 测试无效参数:")
    try:
        crawler = BilibiliWebCrawler(
            cookie_refresh_interval=-1,
            proxy_refresh_interval=0
        )
        config_info = crawler.get_config_info()
        print(f"   无效参数处理结果: {config_info}")
    except Exception as e:
        print(f"   无效参数处理失败: {e}")


def test_api_controller():
    """
    测试API控制器
    """
    print("\n=== 测试API控制器 ===\n")
    
    try:
        print("1. 导入API控制器...")
        from app.api.controller.bilibili_api import BilibiliWebCrawler as api_crawler
        print("   ✓ 导入成功")
        
        print("\n2. 检查爬虫实例...")
        print(f"   爬虫类型: {type(api_crawler).__name__}")
        api_crawler.print_config_info()
        
        print("\n✅ API控制器测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ API控制器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """
    主测试函数
    """
    print("开始测试修复后的BilibiliWebCrawler\n")
    
    # 运行所有测试
    test_crawler_initialization()
    test_request_manager_integration()
    await test_crawler_methods()
    test_config_file_reading()
    test_error_handling()
    test_api_controller()
    
    print("\n测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
