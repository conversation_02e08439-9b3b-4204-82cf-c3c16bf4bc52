# -*- coding: utf-8 -*-
# @Time : 2025/6/28 18:00
# <AUTHOR> Assistant
# @File : check_startup.py

"""
FastAPI 启动前检查脚本
"""

import os
import sys
import yaml
import importlib.util
from typing import List, Tuple


class StartupChecker:
    def __init__(self):
        self.errors = []
        self.warnings = []
        
    def check_file_exists(self, file_path: str, description: str) -> bool:
        """检查文件是否存在"""
        if os.path.exists(file_path):
            print(f"✅ {description}: {file_path}")
            return True
        else:
            self.errors.append(f"❌ {description}不存在: {file_path}")
            return False
    
    def check_config_file(self) -> bool:
        """检查配置文件"""
        print("\n📋 检查配置文件...")
        
        config_path = "config.yaml"
        if not self.check_file_exists(config_path, "主配置文件"):
            return False
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查必要的配置项
            required_configs = [
                ("API.Host_IP", "API主机IP"),
                ("API.Host_Port", "API端口"),
                ("API.Version", "API版本"),
                ("Web.PyWebIO_Enable", "Web界面开关")
            ]
            
            for config_key, description in required_configs:
                keys = config_key.split('.')
                value = config
                try:
                    for key in keys:
                        value = value[key]
                    print(f"✅ {description}: {value}")
                except KeyError:
                    self.errors.append(f"❌ 配置项缺失: {config_key}")
            
            return True
            
        except Exception as e:
            self.errors.append(f"❌ 配置文件解析失败: {e}")
            return False
    
    def check_crawler_configs(self) -> bool:
        """检查爬虫配置文件"""
        print("\n🕷️ 检查爬虫配置文件...")
        
        crawler_configs = [
            ("crawlers/spiders/douyin/config.yaml", "抖音爬虫配置"),
            ("crawlers/spiders/bilibili/config.yaml", "B站爬虫配置"),
        ]
        
        all_exist = True
        for config_path, description in crawler_configs:
            if self.check_file_exists(config_path, description):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)
                    
                    # 检查关键配置
                    if "ProxyConfig" in config:
                        proxy_config = config["ProxyConfig"]
                        if proxy_config.get("proxy_auth_key") and proxy_config.get("proxy_auth_password"):
                            print(f"  ✅ 代理配置完整")
                        else:
                            self.warnings.append(f"⚠️  {description}: 代理认证信息可能未配置")
                    
                    if "VirtualBrowserConfig" in config:
                        browser_config = config["VirtualBrowserConfig"]
                        if browser_config.get("browser_api_key"):
                            print(f"  ✅ 浏览器API配置完整")
                        else:
                            self.warnings.append(f"⚠️  {description}: 浏览器API密钥可能未配置")
                            
                except Exception as e:
                    self.errors.append(f"❌ {description}解析失败: {e}")
                    all_exist = False
            else:
                all_exist = False
        
        return all_exist
    
    def check_dependencies(self) -> bool:
        """检查依赖包"""
        print("\n📦 检查关键依赖包...")
        
        required_packages = [
            ("fastapi", "FastAPI框架"),
            ("uvicorn", "ASGI服务器"),
            ("pydantic", "数据验证"),
            ("yaml", "YAML解析"),
            ("requests", "HTTP请求"),
            ("redis", "Redis客户端"),
            ("pywebio", "Web界面")
        ]
        
        missing_packages = []
        for package, description in required_packages:
            try:
                if package == "yaml":
                    import yaml
                else:
                    __import__(package)
                print(f"✅ {description}: {package}")
            except ImportError:
                missing_packages.append(package)
                self.errors.append(f"❌ 缺少依赖包: {package} ({description})")
        
        return len(missing_packages) == 0
    
    def check_crawler_imports(self) -> bool:
        """检查爬虫模块导入"""
        print("\n🔍 检查爬虫模块导入...")
        
        crawler_modules = [
            ("crawlers.spiders.douyin.web_crawler", "DouyinWebCrawler", "抖音爬虫"),
            ("crawlers.spiders.bilibili.web_crawler", "BilibiliWebCrawler", "B站爬虫"),
            ("crawlers.utils.request_manager", "RequestManager", "请求管理器"),
            ("crawlers.proxy.proxy_cache", "ProxyCache", "代理缓存")
        ]
        
        import_errors = []
        for module_path, class_name, description in crawler_modules:
            try:
                module = __import__(module_path, fromlist=[class_name])
                getattr(module, class_name)
                print(f"✅ {description}: {module_path}.{class_name}")
            except Exception as e:
                import_errors.append(f"❌ {description}导入失败: {e}")
                self.errors.append(f"❌ {description}导入失败: {e}")
        
        return len(import_errors) == 0
    
    def check_api_controllers(self) -> bool:
        """检查API控制器"""
        print("\n🎮 检查API控制器...")
        
        controllers = [
            ("app.api.controller.douyin_api", "抖音API控制器"),
            ("app.api.controller.bilibili_api", "B站API控制器"),
            ("app.api.router", "API路由器"),
            ("app.main", "主应用")
        ]
        
        controller_errors = []
        for module_path, description in controllers:
            try:
                __import__(module_path)
                print(f"✅ {description}: {module_path}")
            except Exception as e:
                controller_errors.append(f"❌ {description}导入失败: {e}")
                self.errors.append(f"❌ {description}导入失败: {e}")
        
        return len(controller_errors) == 0
    
    def check_port_availability(self) -> bool:
        """检查端口可用性"""
        print("\n🔌 检查端口可用性...")
        
        try:
            with open("config.yaml", 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            port = config.get("API", {}).get("Host_Port", 80)
            
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                self.warnings.append(f"⚠️  端口 {port} 已被占用，可能需要停止其他服务")
                return False
            else:
                print(f"✅ 端口 {port} 可用")
                return True
                
        except Exception as e:
            self.warnings.append(f"⚠️  端口检查失败: {e}")
            return True  # 不阻止启动
    
    def run_all_checks(self) -> bool:
        """运行所有检查"""
        print("🔍 FastAPI 启动前检查")
        print("=" * 50)
        
        checks = [
            self.check_config_file,
            self.check_crawler_configs,
            self.check_dependencies,
            self.check_crawler_imports,
            self.check_api_controllers,
            self.check_port_availability
        ]
        
        all_passed = True
        for check in checks:
            try:
                if not check():
                    all_passed = False
            except Exception as e:
                self.errors.append(f"❌ 检查过程出错: {e}")
                all_passed = False
        
        # 打印结果
        print("\n" + "=" * 50)
        print("📊 检查结果:")
        
        if self.errors:
            print(f"\n❌ 发现 {len(self.errors)} 个错误:")
            for error in self.errors:
                print(f"  {error}")
        
        if self.warnings:
            print(f"\n⚠️  发现 {len(self.warnings)} 个警告:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if not self.errors and not self.warnings:
            print("\n🎉 所有检查通过！可以启动服务。")
        elif not self.errors:
            print("\n✅ 基本检查通过，有一些警告但不影响启动。")
        else:
            print("\n❌ 发现严重错误，建议修复后再启动服务。")
        
        return all_passed and len(self.errors) == 0


def main():
    """主函数"""
    checker = StartupChecker()
    success = checker.run_all_checks()
    
    if success:
        print("\n🚀 建议启动命令:")
        print("   python start.py")
        print("\n📚 启动后可访问:")
        print("   API文档: http://localhost/docs")
        print("   ReDoc: http://localhost/redoc")
        sys.exit(0)
    else:
        print("\n🛠️  请修复上述问题后重新检查。")
        sys.exit(1)


if __name__ == "__main__":
    main()
