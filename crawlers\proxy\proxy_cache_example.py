# -*- coding: utf-8 -*-
# @Time : 2025/6/28 16:00
# <AUTHOR> Assistant
# @File : proxy_cache_example.py

"""
IP代理缓存使用示例
"""

import asyncio
import time
from crawlers.proxy.proxy_cache import ProxyCache


async def main():
    """
    演示代理缓存的使用方法
    """
    # 初始化代理缓存管理器
    # 从配置文件中获取认证信息
    auth_key = "A1E0C6DF"  # 替换为实际的认证密钥
    auth_password = "606E1E6F0601"  # 替换为实际的认证密码
    
    proxy_cache = ProxyCache(
        auth_key=auth_key,
        auth_password=auth_password,
        proxy_refresh_interval=1,  # 1分钟过期
        cache_type="redis"  # 使用Redis缓存
    )
    
    print("=== IP代理缓存使用示例 ===\n")
    
    # 1. 第一次获取代理（会从青果代理获取新的）
    print("1. 第一次获取代理:")
    proxy_info = proxy_cache.get_proxy()
    if proxy_info:
        print(f"   代理URL: {proxy_info['proxy_url']}")
        print(f"   过期时间: {proxy_info['deadline']}")
    else:
        print("   获取代理失败")
    
    # 2. 立即再次获取代理（应该从缓存返回）
    print("\n2. 立即再次获取代理（从缓存）:")
    proxy_info = proxy_cache.get_proxy()
    if proxy_info:
        print(f"   代理URL: {proxy_info['proxy_url']}")
        print(f"   过期时间: {proxy_info['deadline']}")
    else:
        print("   获取代理失败")
    
    # 3. 查看代理状态
    print("\n3. 查看代理状态:")
    status = proxy_cache.get_proxy_status()
    print(f"   状态: {status}")
    
    # 4. 模拟代理请求失败
    print("\n4. 模拟代理请求失败:")
    proxy_cache.mark_failed()
    proxy_cache.mark_failed()
    status = proxy_cache.get_proxy_status()
    print(f"   失败次数: {status.get('failed_count', 0)}")
    
    # 5. 清除失败次数
    print("\n5. 清除失败次数:")
    proxy_cache.clear_failed_count()
    status = proxy_cache.get_proxy_status()
    print(f"   失败次数: {status.get('failed_count', 0)}")
    
    # 6. 强制刷新代理
    print("\n6. 强制刷新代理:")
    new_proxy = proxy_cache.force_refresh()
    if new_proxy:
        print(f"   新代理URL: {new_proxy['proxy_url']}")
        print(f"   新过期时间: {new_proxy['deadline']}")
    else:
        print("   强制刷新失败")
    
    # 7. 等待1分钟后再次获取（模拟过期）
    print("\n7. 等待缓存过期后获取代理...")
    print("   (实际使用中不需要等待，这里仅为演示)")
    # time.sleep(61)  # 等待1分钟过期
    # proxy_info = proxy_cache.get_proxy()
    # print(f"   过期后获取的代理: {proxy_info}")


def demo_crawler_usage():
    """
    演示在爬虫中的使用方式
    """
    print("\n=== 爬虫中的使用示例 ===\n")
    
    # 初始化代理缓存
    proxy_cache = ProxyCache(
        auth_key="A1E0C6DF",
        auth_password="606E1E6F0601",
        cache_type="redis"
    )
    
    # 模拟爬虫请求流程
    def make_request_with_proxy():
        """
        模拟使用代理进行网络请求
        """
        # 1. 获取代理
        proxy_info = proxy_cache.get_proxy()
        if not proxy_info:
            print("   无法获取代理，请求失败")
            return False
        
        print(f"   使用代理: {proxy_info['proxy_url']}")
        
        # 2. 模拟网络请求
        try:
            # 这里应该是实际的网络请求代码
            # response = requests.get(url, proxies={"http": proxy_info['proxy_url'], "https": proxy_info['proxy_url']})
            
            # 模拟请求成功
            success = True  # 实际应该根据response判断
            
            if success:
                print("   请求成功")
                proxy_cache.clear_failed_count()  # 清除失败次数
                return True
            else:
                print("   请求失败")
                proxy_cache.mark_failed()  # 标记失败
                return False
                
        except Exception as e:
            print(f"   请求异常: {e}")
            proxy_cache.mark_failed()  # 标记失败
            return False
    
    # 执行多次请求
    for i in range(3):
        print(f"第{i+1}次请求:")
        make_request_with_proxy()
        print()


if __name__ == "__main__":
    # 运行异步示例
    asyncio.run(main())
    
    # 运行爬虫使用示例
    demo_crawler_usage()
