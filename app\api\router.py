from fastapi import APIRouter
from app.api.controller import (
    tiktok_web_api,
    tiktok_app_api,
    douyin_api,
    bilibili_api,
    hybrid_parsing,
    download,
    health_api,
)

router = APIRouter()

# TikTok routers
router.include_router(
    tiktok_web_api.router, prefix="/tiktok/web", tags=["TikTok-Web-API"]
)
router.include_router(
    tiktok_app_api.router, prefix="/tiktok/app", tags=["TikTok-App-API"]
)

# Douyin routers
router.include_router(douyin_api.router, prefix="/douyin/web", tags=["Douyin-Web-API"])

# Bilibili routers
router.include_router(
    bilibili_api.router, prefix="/bilibili/web", tags=["Bilibili-Web-API"]
)

# Hybrid routers
router.include_router(hybrid_parsing.router, prefix="/hybrid", tags=["Hybrid-API"])

# Download routers
router.include_router(download.router, tags=["Download"])

# Health check routers
router.include_router(health_api.router, prefix="/health", tags=["Health"])
