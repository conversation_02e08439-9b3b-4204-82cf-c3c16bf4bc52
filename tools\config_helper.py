# -*- encoding:utf-8 -*-
import os
from pathlib import Path

import yaml


def find_project_root(start_path: Path) -> Path:
    current_path = start_path.resolve()
    while current_path != current_path.parent:  # 直到到达文件系统根目录
        if (current_path / "pyproject.toml").exists():
            return current_path
        current_path = current_path.parent
    raise FileNotFoundError("未找到项目根目录（未发现 pyproject.toml）")


root_project = find_project_root(Path(__file__).parent)
config_path = os.path.join(root_project, 'config.yaml')

with open(config_path, "r", encoding="utf-8") as f:
    root_config = yaml.safe_load(f)

log_dir = root_config["log"]["log_dir"]
