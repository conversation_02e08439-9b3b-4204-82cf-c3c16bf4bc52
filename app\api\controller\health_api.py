# -*- coding: utf-8 -*-
"""
健康检查API
"""
from fastapi import APIRouter, Request
from app.api.models.APIResponseModel import ResponseModel
from app.startup_checks import get_startup_status

router = APIRouter()


@router.get(
    "/health",
    response_model=ResponseModel,
    summary="健康检查/Health Check",
)
async def health_check(request: Request):
    """
    # [中文]
    ### 用途:
    - 检查应用健康状态
    - 检查Redis连接状态
    - 检查虚拟浏览器API状态
    - 检查Cookie缓存状态
    ### 返回:
    - 应用健康状态信息

    # [English]
    ### Purpose:
    - Check application health status
    - Check Redis connection status
    - Check virtual browser API status
    - Check cookie cache status
    ### Return:
    - Application health status information
    """
    try:
        status = get_startup_status()
        return ResponseModel(code=200, router=request.url.path, data=status)
    except Exception as e:
        return ResponseModel(
            code=500, 
            router=request.url.path, 
            data={"error": str(e), "status": "unhealthy"}
        )


@router.get(
    "/ready",
    response_model=ResponseModel,
    summary="就绪检查/Readiness Check",
)
async def readiness_check(request: Request):
    """
    # [中文]
    ### 用途:
    - 检查应用是否准备好接收请求
    - 主要检查关键依赖服务状态
    ### 返回:
    - 应用就绪状态

    # [English]
    ### Purpose:
    - Check if application is ready to receive requests
    - Mainly check critical dependency service status
    ### Return:
    - Application readiness status
    """
    try:
        status = get_startup_status()
        
        # 检查关键服务是否可用
        is_ready = (
            status.get("redis_connection", False) or 
            status.get("virtual_browser_api", False)
        )
        
        if is_ready:
            return ResponseModel(
                code=200, 
                router=request.url.path, 
                data={"status": "ready", "details": status}
            )
        else:
            return ResponseModel(
                code=503, 
                router=request.url.path, 
                data={"status": "not_ready", "details": status}
            )
            
    except Exception as e:
        return ResponseModel(
            code=503, 
            router=request.url.path, 
            data={"status": "not_ready", "error": str(e)}
        )
