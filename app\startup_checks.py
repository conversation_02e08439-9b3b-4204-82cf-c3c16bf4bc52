# -*- coding: utf-8 -*-
"""
FastAPI启动时的检查和初始化模块
"""
import asyncio
import logging
from typing import Dict, Any

from crawlers.utils.request_manager import RequestManager
from crawlers.cache.cache_factory import CacheFactory


class StartupChecker:
    """启动检查器，用于在FastAPI启动时进行必要的检查和预热"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def check_redis_connection(self) -> bool:
        """检查Redis连接"""
        try:
            cache = CacheFactory.create_cache("redis")
            # 测试连接
            cache.set("startup_test", "ok", 10)
            result = cache.get("startup_test")
            cache.delete("startup_test")
            return result == "ok"
        except Exception as e:
            self.logger.error(f"Redis连接检查失败: {e}")
            return False
    
    async def preload_cookies(self, platforms: list = None) -> Dict[str, bool]:
        """预加载cookie，避免首次请求时的延迟"""
        if platforms is None:
            platforms = ["douyin", "tiktok", "bilibili"]
            
        results = {}
        
        # 这里不实际生成cookie，只是检查缓存中是否有有效cookie
        try:
            cache = CacheFactory.create_cache("redis")
            for platform in platforms:
                cookie_key = f"cookie:{platform}"
                cached_cookie = cache.get(cookie_key)
                results[platform] = cached_cookie is not None
                
                if not cached_cookie:
                    self.logger.warning(f"平台 {platform} 没有缓存的cookie，首次请求可能较慢")
                else:
                    self.logger.info(f"平台 {platform} 已有缓存cookie")
                    
        except Exception as e:
            self.logger.error(f"预加载cookie检查失败: {e}")
            for platform in platforms:
                results[platform] = False
                
        return results
    
    async def check_virtual_browser_api(self) -> bool:
        """检查虚拟浏览器API是否可用"""
        try:
            import requests
            response = requests.get("http://localhost:9000/api/getBrowserList", 
                                  timeout=5,
                                  headers={'Content-Type': 'application/json'})
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"虚拟浏览器API检查失败: {e}")
            return False
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有启动检查"""
        self.logger.info("开始启动检查...")
        
        results = {
            "redis_connection": await self.check_redis_connection(),
            "virtual_browser_api": await self.check_virtual_browser_api(),
            "cookie_cache_status": await self.preload_cookies(),
            "startup_time": None
        }
        
        # 记录检查结果
        for check_name, result in results.items():
            if check_name == "cookie_cache_status":
                continue
            status = "✓" if result else "✗"
            self.logger.info(f"{check_name}: {status}")
            
        # 如果关键服务不可用，给出警告
        if not results["redis_connection"]:
            self.logger.warning("Redis连接失败，cookie和代理缓存将使用内存模式")
            
        if not results["virtual_browser_api"]:
            self.logger.warning("虚拟浏览器API不可用，cookie生成功能可能受影响")
            
        self.logger.info("启动检查完成")
        return results


# 全局启动检查器实例
startup_checker = StartupChecker()


async def startup_event():
    """FastAPI启动事件处理函数"""
    return await startup_checker.run_all_checks()


def get_startup_status() -> Dict[str, Any]:
    """获取启动状态（同步版本，用于健康检查接口）"""
    try:
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(startup_checker.run_all_checks())
    except Exception as e:
        logging.error(f"获取启动状态失败: {e}")
        return {"error": str(e)}
