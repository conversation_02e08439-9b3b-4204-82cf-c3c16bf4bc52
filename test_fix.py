# -*- coding: utf-8 -*-
# @Time : 2025/6/28 17:00
# <AUTHOR> Assistant
# @File : test_fix.py

"""
测试修复后的DouyinWebCrawler - 在项目根目录运行
"""


def test_douyin_crawler():
    """测试抖音爬虫"""
    print("=== 测试DouyinWebCrawler修复 ===\n")

    try:
        print("1. 导入DouyinWebCrawler...")
        from crawlers.spiders.douyin.web_crawler import DouyinWebCrawler

        print("   ✓ 导入成功")

        print("\n2. 使用配置文件默认值初始化...")
        crawler = DouyinWebCrawler()
        print("   ✓ 初始化成功")

        print("\n3. 显示配置信息...")
        crawler.print_config_info()

        print("\n4. 测试RequestManager集成...")
        print(f"   RequestManager类型: {type(crawler.request_manager).__name__}")
        print(f"   代理缓存类型: {type(crawler.request_manager.proxy_cache).__name__}")
        print(
            f"   Cookie缓存类型: {type(crawler.request_manager.cookie_cache).__name__}"
        )

        print("\n5. 测试代理状态...")
        try:
            proxy_status = crawler.request_manager.get_proxy_status()
            print(f"   代理状态: {proxy_status.get('status', 'unknown')}")
        except Exception as e:
            print(f"   代理状态获取失败: {e}")

        print("\n6. 测试模型实例化...")
        from crawlers.spiders.douyin.models import BaseRequestModel, PostDetail

        base_model = BaseRequestModel()
        print(f"   BaseRequestModel msToken: {base_model.msToken[:20]}...")

        post_detail = PostDetail(aweme_id="test123")
        print(f"   PostDetail aweme_id: {post_detail.aweme_id}")

        print("\n7. 测试自定义参数初始化...")
        custom_crawler = DouyinWebCrawler(
            browser_api_key="custom_browser_key",
            proxy_auth_key="custom_proxy_key",
            cookie_refresh_interval=60,
        )
        print("   ✓ 自定义参数初始化成功")
        custom_crawler.print_config_info()

        print("\n✅ 所有测试通过！DouyinWebCrawler修复成功！")
        return True

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_proxy_cache():
    """测试代理缓存"""
    print("\n=== 测试代理缓存机制 ===\n")

    try:
        print("1. 导入代理缓存...")
        from crawlers.proxy.proxy_cache import ProxyCache

        print("   ✓ 导入成功")

        print("\n2. 初始化代理缓存...")
        proxy_cache = ProxyCache(
            auth_key="test_key", auth_password="test_password", cache_type="memory"
        )
        print("   ✓ 初始化成功")

        print("\n3. 测试代理状态...")
        status = proxy_cache.get_proxy_status()
        print(f"   代理状态: {status}")

        print("\n✅ 代理缓存测试通过！")
        return True

    except Exception as e:
        print(f"\n❌ 代理缓存测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_api_controller():
    """测试API控制器"""
    print("\n=== 测试API控制器 ===\n")

    try:
        print("1. 导入API控制器...")
        from app.api.controller.douyin_api import crawler

        print("   ✓ 导入成功")

        print("\n2. 检查爬虫实例...")
        print(f"   爬虫类型: {type(crawler).__name__}")
        crawler.print_config_info()

        print("\n✅ API控制器测试通过！")
        return True

    except Exception as e:
        print(f"\n❌ API控制器测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_service():
    """测试服务层"""
    print("\n=== 测试服务层 ===\n")

    try:
        print("1. 导入DyService...")
        from services.douyin_service import DyService

        print("   ✓ 导入成功")

        print("\n2. 初始化服务...")
        service = DyService()
        print(f"   ✓ 初始化成功，爬虫数量: {len(service.crawlers)}")

        print("\n3. 检查爬虫实例...")
        if service.crawlers:
            first_crawler = service.crawlers[0]
            print(f"   第一个爬虫类型: {type(first_crawler).__name__}")
            first_crawler.print_config_info()

        print("\n✅ 服务层测试通过！")
        return True

    except Exception as e:
        print(f"\n❌ 服务层测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_bilibili_crawler():
    """测试Bilibili爬虫"""
    print("\n=== 测试BilibiliWebCrawler修复 ===\n")

    try:
        print("1. 导入BilibiliWebCrawler...")
        from crawlers.spiders.bilibili.web_crawler import BilibiliWebCrawler

        print("   ✓ 导入成功")

        print("\n2. 使用配置文件默认值初始化...")
        crawler = BilibiliWebCrawler()
        print("   ✓ 初始化成功")

        print("\n3. 显示配置信息...")
        crawler.print_config_info()

        print("\n4. 测试RequestManager集成...")
        print(f"   RequestManager类型: {type(crawler.request_manager).__name__}")
        print(f"   代理缓存类型: {type(crawler.request_manager.proxy_cache).__name__}")
        print(
            f"   Cookie缓存类型: {type(crawler.request_manager.cookie_cache).__name__}"
        )

        print("\n5. 测试代理状态...")
        try:
            proxy_status = crawler.request_manager.get_proxy_status()
            print(f"   代理状态: {proxy_status.get('status', 'unknown')}")
        except Exception as e:
            print(f"   代理状态获取失败: {e}")

        print("\n6. 测试自定义参数初始化...")
        custom_crawler = BilibiliWebCrawler(
            browser_api_key="custom_browser_key",
            proxy_auth_key="custom_proxy_key",
            cookie_refresh_interval=60,
        )
        print("   ✓ 自定义参数初始化成功")
        custom_crawler.print_config_info()

        print("\n✅ BilibiliWebCrawler测试通过！")
        return True

    except Exception as e:
        print(f"\n❌ BilibiliWebCrawler测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_bilibili_api_controller():
    """测试Bilibili API控制器"""
    print("\n=== 测试Bilibili API控制器 ===\n")

    try:
        print("1. 导入Bilibili API控制器...")
        from app.api.controller.bilibili_api import BilibiliWebCrawler as api_crawler

        print("   ✓ 导入成功")

        print("\n2. 检查爬虫实例...")
        print(f"   爬虫类型: {type(api_crawler).__name__}")
        api_crawler.print_config_info()

        print("\n✅ Bilibili API控制器测试通过！")
        return True

    except Exception as e:
        print(f"\n❌ Bilibili API控制器测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("开始测试所有修复...\n")

    results = []
    results.append(test_douyin_crawler())
    results.append(test_proxy_cache())
    results.append(test_api_controller())
    results.append(test_service())
    results.append(test_bilibili_crawler())
    results.append(test_bilibili_api_controller())

    success_count = sum(results)
    total_count = len(results)

    print(f"\n{'='*50}")
    print(f"测试结果: {success_count}/{total_count} 通过")

    if success_count == total_count:
        print("🎉 所有测试都通过了！修复完全成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")

    print("测试完成。")
